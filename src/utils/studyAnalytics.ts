import { getStudySessions } from './supabase';

// Types for study analytics
export interface StudySession {
  id: string;
  subject: string;
  duration: number; // in seconds
  mode: "pomodoro" | "stopwatch";
  phase: "work" | "shortBreak" | "longBreak";
  completed: boolean;
  date: string;
  start_time: string;
  end_time?: string;
  user_id: string;
}

export interface DailyStat {
  date: string;
  totalDuration: number; // in seconds
  subjectDurations: { [subject: string]: number };
  completedPomodoros: number;
}

export interface StreakData {
  currentStreak: number;
  longestStreak: number;
  streakMap: { [date: string]: boolean };
  nextMilestone: number;
  milestones: number[];
}

export interface PrepInsightMetrics {
  averageDailyStudyTime: {
    value: number; // in seconds
    trend: number; // percentage change from previous week
    comparisonText: string;
  };
  highestStudyHours: {
    value: number; // in seconds
    date: string;
    subject?: string;
    isNewRecord: boolean;
  };
  currentStudyStreak: StreakData;
  todayStudyTime: number; // in seconds
  weeklyTotal: number; // in seconds
  isLoading: boolean;
  error: string | null;
}

// Utility function to format date to YYYY-MM-DD
export const formatDateToLocalYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Get date range for calculations
export const getDateRange = (days: number): { startDate: string; endDate: string } => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - days + 1);
  
  return {
    startDate: formatDateToLocalYYYYMMDD(startDate),
    endDate: formatDateToLocalYYYYMMDD(endDate)
  };
};

// Process study sessions into daily stats
export const processDailyStats = (sessions: StudySession[]): DailyStat[] => {
  const dailyStats: { [key: string]: DailyStat } = {};

  sessions.forEach(session => {
    if (!session.duration || session.duration < 0 || !session.date || !session.subject) return;

    if (!dailyStats[session.date]) {
      dailyStats[session.date] = {
        date: session.date,
        totalDuration: 0,
        subjectDurations: {},
        completedPomodoros: 0
      };
    }

    const dayStat = dailyStats[session.date];
    dayStat.totalDuration += session.duration;
    dayStat.subjectDurations[session.subject] = (dayStat.subjectDurations[session.subject] || 0) + session.duration;
    
    if (session.completed && session.mode === "pomodoro") {
      dayStat.completedPomodoros++;
    }
  });

  return Object.values(dailyStats).sort((a, b) => a.date.localeCompare(b.date));
};

// Calculate streak information (extracted from Analytics.tsx)
export const calculateStreakInfo = (dailyStats: DailyStat[]): StreakData => {
  const streakMap: { [date: string]: boolean } = {};
  let longestStreak = 0;
  let tempCurrentStreak = 0;

  if (!dailyStats || dailyStats.length === 0) {
    return { 
      currentStreak: 0, 
      longestStreak: 0, 
      streakMap, 
      nextMilestone: 7,
      milestones: [7, 14, 30, 60, 100, 365]
    };
  }

  // Sort by date to ensure chronological order
  const sortedStats = [...dailyStats].sort((a, b) => a.date.localeCompare(b.date));
  let previousDate: Date | null = null;

  // Calculate longest streak
  for (const stat of sortedStats) {
    const currentDate = new Date(stat.date);

    if (stat.totalDuration > 0) {
      if (previousDate) {
        const diffTime = currentDate.getTime() - previousDate.getTime();
        const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
          tempCurrentStreak++;
        } else {
          longestStreak = Math.max(longestStreak, tempCurrentStreak);
          tempCurrentStreak = 1;
        }
      } else {
        tempCurrentStreak = 1;
      }
      previousDate = currentDate;
    } else {
      longestStreak = Math.max(longestStreak, tempCurrentStreak);
      tempCurrentStreak = 0;
      previousDate = currentDate;
    }
  }

  longestStreak = Math.max(longestStreak, tempCurrentStreak);

  // Calculate current streak (from today backwards)
  let finalCurrentStreak = 0;
  const today = new Date();
  const statsMap = new Map(sortedStats.map(stat => [stat.date, stat]));

  // Check if today has study time
  const todayString = formatDateToLocalYYYYMMDD(today);
  const todayStat = statsMap.get(todayString);
  
  let dateToCheck = today;
  if (!todayStat || todayStat.totalDuration === 0) {
    // If no study today, start from yesterday
    dateToCheck.setDate(dateToCheck.getDate() - 1);
  }

  // Count backwards to find current streak
  let tempDate = new Date(dateToCheck);
  while (true) {
    const dateString = formatDateToLocalYYYYMMDD(tempDate);
    const dayStat = statsMap.get(dateString);

    if (dayStat && dayStat.totalDuration > 0) {
      finalCurrentStreak++;
      streakMap[dateString] = true;
      tempDate.setDate(tempDate.getDate() - 1);
    } else {
      break;
    }
  }

  const milestones = [7, 14, 30, 60, 100, 365];
  const nextMilestone = milestones.find(m => m > finalCurrentStreak) || milestones[milestones.length - 1];

  return {
    currentStreak: finalCurrentStreak,
    longestStreak,
    streakMap,
    nextMilestone,
    milestones
  };
};

// Calculate average daily study time for a period
export const calculateAverageStudyTime = (dailyStats: DailyStat[], days: number): number => {
  if (!dailyStats || dailyStats.length === 0) return 0;

  const { startDate } = getDateRange(days);
  const relevantStats = dailyStats.filter(stat => stat.date >= startDate);
  
  const totalDuration = relevantStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
  return totalDuration / days; // Average per day
};

// Find highest study hours record
export const findHighestStudyHours = (dailyStats: DailyStat[]): {
  value: number;
  date: string;
  subject?: string;
  isNewRecord: boolean;
} => {
  if (!dailyStats || dailyStats.length === 0) {
    return { value: 0, date: '', isNewRecord: false };
  }

  let maxStat = dailyStats[0];
  for (const stat of dailyStats) {
    if (stat.totalDuration > maxStat.totalDuration) {
      maxStat = stat;
    }
  }

  // Find the subject with most time on that day
  let topSubject = '';
  let maxSubjectTime = 0;
  for (const [subject, duration] of Object.entries(maxStat.subjectDurations)) {
    if (duration > maxSubjectTime) {
      maxSubjectTime = duration;
      topSubject = subject;
    }
  }

  // Check if it's a recent record (within last 30 days)
  const recordDate = new Date(maxStat.date);
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const isNewRecord = recordDate >= thirtyDaysAgo;

  return {
    value: maxStat.totalDuration,
    date: maxStat.date,
    subject: topSubject || undefined,
    isNewRecord
  };
};

// Calculate today's study time
export const calculateTodayStudyTime = (sessions: StudySession[]): number => {
  const today = formatDateToLocalYYYYMMDD(new Date());
  return sessions
    .filter(session => session.date === today)
    .reduce((total, session) => total + session.duration, 0);
};

// Calculate weekly total
export const calculateWeeklyTotal = (dailyStats: DailyStat[]): number => {
  const { startDate } = getDateRange(7);
  return dailyStats
    .filter(stat => stat.date >= startDate)
    .reduce((total, stat) => total + stat.totalDuration, 0);
};

// Calculate weekly trend (percentage change from previous week)
export const calculateWeeklyTrend = (dailyStats: DailyStat[]): number => {
  const currentWeekRange = getDateRange(7);
  const previousWeekStart = new Date();
  previousWeekStart.setDate(previousWeekStart.getDate() - 13);
  const previousWeekEnd = new Date();
  previousWeekEnd.setDate(previousWeekEnd.getDate() - 7);

  const currentWeekTotal = dailyStats
    .filter(stat => stat.date >= currentWeekRange.startDate && stat.date <= currentWeekRange.endDate)
    .reduce((total, stat) => total + stat.totalDuration, 0);

  const previousWeekTotal = dailyStats
    .filter(stat => {
      const statDate = stat.date;
      const prevStart = formatDateToLocalYYYYMMDD(previousWeekStart);
      const prevEnd = formatDateToLocalYYYYMMDD(previousWeekEnd);
      return statDate >= prevStart && statDate <= prevEnd;
    })
    .reduce((total, stat) => total + stat.totalDuration, 0);

  if (previousWeekTotal === 0) return currentWeekTotal > 0 ? 100 : 0;
  return ((currentWeekTotal - previousWeekTotal) / previousWeekTotal) * 100;
};

// Main function to process all prep insight metrics
export const calculatePrepInsightMetrics = async (userId: string): Promise<PrepInsightMetrics> => {
  try {
    // Fetch study sessions from Supabase
    const sessions = await getStudySessions(userId);
    
    // Process into daily stats
    const dailyStats = processDailyStats(sessions);
    
    // Calculate all metrics
    const averageDaily = calculateAverageStudyTime(dailyStats, 7);
    const weeklyTrend = calculateWeeklyTrend(dailyStats);
    const highestHours = findHighestStudyHours(dailyStats);
    const streakData = calculateStreakInfo(dailyStats);
    const todayTime = calculateTodayStudyTime(sessions);
    const weeklyTotal = calculateWeeklyTotal(dailyStats);

    return {
      averageDailyStudyTime: {
        value: averageDaily,
        trend: weeklyTrend,
        comparisonText: 'vs last week'
      },
      highestStudyHours: highestHours,
      currentStudyStreak: streakData,
      todayStudyTime: todayTime,
      weeklyTotal: weeklyTotal,
      isLoading: false,
      error: null
    };
  } catch (error) {
    console.error('Error calculating prep insight metrics:', error);
    return {
      averageDailyStudyTime: { value: 0, trend: 0, comparisonText: 'vs last week' },
      highestStudyHours: { value: 0, date: '', isNewRecord: false },
      currentStudyStreak: { 
        currentStreak: 0, 
        longestStreak: 0, 
        streakMap: {}, 
        nextMilestone: 7,
        milestones: [7, 14, 30, 60, 100, 365]
      },
      todayStudyTime: 0,
      weeklyTotal: 0,
      isLoading: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
