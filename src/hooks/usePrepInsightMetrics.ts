import { useState, useEffect, useCallback } from 'react';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { useLocalStorage } from './useLocalStorage';
import { calculatePrepInsightMetrics, PrepInsightMetrics } from '../utils/studyAnalytics';
import { supabase } from '../integrations/supabase/client';

interface CachedMetrics {
  data: PrepInsightMetrics;
  timestamp: number;
  userId: string;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const CACHE_KEY = 'prep-insight-metrics';

/**
 * Custom hook for fetching and managing Prep Insight Board metrics
 * Provides real-time data with caching and optimistic updates
 */
export const usePrepInsightMetrics = () => {
  const { user } = useSupabaseAuth();
  const [metrics, setMetrics] = useState<PrepInsightMetrics>({
    averageDailyStudyTime: { value: 0, trend: 0, comparisonText: 'vs last week' },
    highestStudyHours: { value: 0, date: '', isNewRecord: false },
    currentStudyStreak: { 
      currentStreak: 0, 
      longestStreak: 0, 
      streakMap: {}, 
      nextMilestone: 7,
      milestones: [7, 14, 30, 60, 100, 365]
    },
    todayStudyTime: 0,
    weeklyTotal: 0,
    isLoading: true,
    error: null
  });

  const [cachedMetrics, setCachedMetrics] = useLocalStorage<CachedMetrics | null>(CACHE_KEY, null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Check if cached data is valid
  const isCacheValid = useCallback((cache: CachedMetrics | null): boolean => {
    if (!cache || !user) return false;
    if (cache.userId !== user.id) return false;
    
    const now = Date.now();
    const cacheAge = now - cache.timestamp;
    return cacheAge < CACHE_DURATION;
  }, [user]);

  // Load cached data if valid
  useEffect(() => {
    if (isCacheValid(cachedMetrics)) {
      setMetrics(cachedMetrics!.data);
    }
  }, [cachedMetrics, isCacheValid]);

  // Fetch fresh metrics from Supabase
  const fetchMetrics = useCallback(async (showLoading = true) => {
    if (!user) return;

    try {
      if (showLoading) {
        setMetrics(prev => ({ ...prev, isLoading: true, error: null }));
      } else {
        setIsRefreshing(true);
      }

      const freshMetrics = await calculatePrepInsightMetrics(user.id);
      
      // Update state
      setMetrics(freshMetrics);

      // Update cache
      const cacheData: CachedMetrics = {
        data: freshMetrics,
        timestamp: Date.now(),
        userId: user.id
      };
      setCachedMetrics(cacheData);

    } catch (error) {
      console.error('Error fetching prep insight metrics:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load metrics';
      
      setMetrics(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
    } finally {
      setIsRefreshing(false);
    }
  }, [user, setCachedMetrics]);

  // Initial data fetch
  useEffect(() => {
    if (user && !isCacheValid(cachedMetrics)) {
      fetchMetrics(true);
    } else if (user && isCacheValid(cachedMetrics)) {
      // Load from cache but fetch fresh data in background
      fetchMetrics(false);
    }
  }, [user, fetchMetrics, isCacheValid, cachedMetrics]);

  // Set up real-time subscription for study sessions
  useEffect(() => {
    if (!user) return;

    const subscription = supabase
      .channel('study_sessions_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'study_sessions',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          console.log('Study session change detected:', payload);
          // Refresh metrics when study sessions change
          fetchMetrics(false);
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user, fetchMetrics]);

  // Manual refresh function
  const refreshMetrics = useCallback(() => {
    fetchMetrics(false);
  }, [fetchMetrics]);

  // Clear cache function
  const clearCache = useCallback(() => {
    setCachedMetrics(null);
    if (user) {
      fetchMetrics(true);
    }
  }, [setCachedMetrics, user, fetchMetrics]);

  // Retry function for error states
  const retry = useCallback(() => {
    if (user) {
      fetchMetrics(true);
    }
  }, [user, fetchMetrics]);

  // Optimistic update for new study session
  const optimisticUpdateForNewSession = useCallback((sessionDuration: number, subject?: string) => {
    setMetrics(prev => ({
      ...prev,
      todayStudyTime: prev.todayStudyTime + sessionDuration,
      weeklyTotal: prev.weeklyTotal + sessionDuration,
      // Note: We don't update averages or streaks optimistically as they require more complex calculations
    }));

    // Fetch fresh data after a short delay to get accurate calculations
    setTimeout(() => {
      fetchMetrics(false);
    }, 1000);
  }, [fetchMetrics]);

  // Check if data is stale (older than cache duration)
  const isDataStale = useCallback((): boolean => {
    if (!cachedMetrics) return true;
    const now = Date.now();
    const cacheAge = now - cachedMetrics.timestamp;
    return cacheAge > CACHE_DURATION;
  }, [cachedMetrics]);

  // Get cache age in minutes
  const getCacheAge = useCallback((): number => {
    if (!cachedMetrics) return 0;
    const now = Date.now();
    const cacheAge = now - cachedMetrics.timestamp;
    return Math.floor(cacheAge / (1000 * 60)); // Convert to minutes
  }, [cachedMetrics]);

  return {
    // Data
    metrics,
    
    // Loading states
    isLoading: metrics.isLoading,
    isRefreshing,
    error: metrics.error,
    
    // Cache info
    isDataStale: isDataStale(),
    cacheAge: getCacheAge(),
    
    // Actions
    refreshMetrics,
    clearCache,
    retry,
    optimisticUpdateForNewSession,
    
    // Computed values for easy access
    hasData: !metrics.isLoading && !metrics.error,
    isEmpty: !metrics.isLoading && !metrics.error && 
             metrics.todayStudyTime === 0 && 
             metrics.currentStudyStreak.currentStreak === 0 &&
             metrics.highestStudyHours.value === 0
  };
};

export default usePrepInsightMetrics;
