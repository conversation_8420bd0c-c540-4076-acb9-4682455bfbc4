import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { BarChart3, Flame, Trophy, TrendingUp, Clock, Target, RefreshCw, AlertCircle, Loader2 } from 'lucide-react';
import { usePrepInsightMetrics } from '../../hooks/usePrepInsightMetrics';
import { formatAnalyticsTime, formatTrendPercentage, getRelativeTimeDescription, formatMilestoneText } from '../../utils/timeFormatting';

export const PrepInsightBoardWidget: React.FC = () => {
  const navigate = useNavigate();
  const {
    metrics,
    isLoading,
    isRefreshing,
    error,
    hasData,
    isEmpty,
    refreshMetrics,
    retry
  } = usePrepInsightMetrics();

  // Handle navigation to Analytics page
  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  // Handle navigation to Analytics page with streak focus
  const handleViewHistory = () => {
    navigate('/analytics?tab=overview&focus=streak');
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="space-y-6">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 animate-pulse">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div>
              <div className="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
          <div className="text-right">
            <div className="w-16 h-6 bg-gray-200 dark:bg-gray-700 rounded mb-1"></div>
            <div className="w-12 h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      ))}
    </div>
  );

  // Error component
  const ErrorDisplay = () => (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        Failed to Load Metrics
      </h3>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        {error || 'Unable to fetch your study insights'}
      </p>
      <button
        onClick={retry}
        className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm rounded-lg transition-colors duration-200"
      >
        Try Again
      </button>
    </div>
  );

  // Empty state component
  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <BarChart3 className="h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        No Study Data Yet
      </h3>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Start studying to see your insights and streaks here
      </p>
      <button
        onClick={handleViewAnalytics}
        className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm rounded-lg transition-colors duration-200"
      >
        View Analytics
      </button>
    </div>
  );

  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-teal-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <BarChart3 className="h-6 w-6 text-teal-500" />
            <span className="text-gray-800 dark:text-gray-200 text-xl">
              Prep Insight Board
            </span>
          </div>
          {isRefreshing && (
            <Loader2 className="h-4 w-4 text-teal-500 animate-spin" />
          )}
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Your study metrics, streaks, and performance insights
        </p>
      </CardHeader>
      <CardContent className="pb-6">
        {/* Loading State */}
        {isLoading && <LoadingSkeleton />}

        {/* Error State */}
        {error && !isLoading && <ErrorDisplay />}

        {/* Empty State */}
        {isEmpty && !isLoading && !error && <EmptyState />}

        {/* Metrics Grid */}
        {hasData && !isLoading && (
          <div className="space-y-6">
            {/* Average Daily Study Time */}
            <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/10 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                  <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-700 dark:text-blue-400">Avg Time Studied</p>
                  <p className="text-xs text-blue-600 dark:text-blue-500">Per day (7-day average)</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {formatAnalyticsTime(metrics.averageDailyStudyTime.value)}
                </p>
                <div className="flex items-center gap-1">
                  {(() => {
                    const trend = formatTrendPercentage(metrics.averageDailyStudyTime.trend);
                    const TrendIcon = trend.trend === 'up' ? TrendingUp :
                                    trend.trend === 'down' ? TrendingUp :
                                    TrendingUp;
                    return (
                      <>
                        <TrendIcon className={`h-3 w-3 ${trend.color} ${trend.trend === 'down' ? 'rotate-180' : ''}`} />
                        <span className={`text-xs ${trend.color}`}>{trend.text}</span>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>

            {/* Highest Study Hours */}
            <div className="flex items-center justify-between p-4 bg-amber-50 dark:bg-amber-900/10 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center">
                  <Trophy className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-amber-700 dark:text-amber-400">Highest Study Hours</p>
                  <p className="text-xs text-amber-600 dark:text-amber-500">
                    Personal record
                    {metrics.highestStudyHours.isNewRecord && (
                      <span className="ml-1 px-1 py-0.5 bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200 text-xs rounded">NEW!</span>
                    )}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-amber-900 dark:text-amber-100">
                  {formatAnalyticsTime(metrics.highestStudyHours.value)}
                </p>
                {metrics.highestStudyHours.date && (
                  <p className="text-xs text-amber-600 dark:text-amber-500">
                    {getRelativeTimeDescription(metrics.highestStudyHours.date)}
                  </p>
                )}
              </div>
            </div>

            {/* Study Streak */}
            <div className="flex items-center justify-between p-4 bg-emerald-50 dark:bg-emerald-900/10 rounded-lg border border-emerald-200 dark:border-emerald-800">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-emerald-100 dark:bg-emerald-800 rounded-full flex items-center justify-center">
                  <Flame className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-emerald-700 dark:text-emerald-400">Study Streak</p>
                  <p className="text-xs text-emerald-600 dark:text-emerald-500">
                    Current streak
                    {metrics.currentStudyStreak.currentStreak >= 7 && (
                      <span className="ml-1 px-1 py-0.5 bg-emerald-200 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-200 text-xs rounded">🔥</span>
                    )}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-emerald-900 dark:text-emerald-100">
                  {metrics.currentStudyStreak.currentStreak} days
                </p>
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3 text-emerald-500" />
                  <span className="text-xs text-emerald-600">
                    Next: {formatMilestoneText(metrics.currentStudyStreak.nextMilestone)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mini Trend Chart and Today's Progress */}
        {hasData && !isLoading && (
          <div className="mt-6 space-y-4">
            {/* Today's Progress */}
            <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Today's Progress</span>
                <Clock className="h-4 w-4 text-teal-500" />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {formatAnalyticsTime(metrics.todayStudyTime)}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {metrics.averageDailyStudyTime.value > 0 && (
                      `${Math.round((metrics.todayStudyTime / metrics.averageDailyStudyTime.value) * 100)}% of avg`
                    )}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600 dark:text-gray-400">This Week</p>
                  <p className="text-lg font-semibold text-teal-600 dark:text-teal-400">
                    {formatAnalyticsTime(metrics.weeklyTotal)}
                  </p>
                </div>
              </div>
            </div>

            {/* Streak Progress */}
            {metrics.currentStudyStreak.currentStreak > 0 && (
              <div className="p-4 bg-emerald-50 dark:bg-emerald-900/10 rounded-lg border border-emerald-200 dark:border-emerald-800">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-emerald-700 dark:text-emerald-400">Streak Progress</span>
                  <Flame className="h-4 w-4 text-emerald-500" />
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-emerald-200 dark:bg-emerald-800 rounded-full h-2">
                    <div
                      className="bg-emerald-500 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min((metrics.currentStudyStreak.currentStreak / metrics.currentStudyStreak.nextMilestone) * 100, 100)}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                    {metrics.currentStudyStreak.currentStreak}/{metrics.currentStudyStreak.nextMilestone}
                  </span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-4 flex gap-2">
          <button
            onClick={handleViewAnalytics}
            disabled={isLoading}
            className="flex-1 px-3 py-2 bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white text-sm rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <BarChart3 className="h-4 w-4" />
            )}
            View Analytics
          </button>
          <button
            onClick={handleViewHistory}
            disabled={isLoading}
            className="px-3 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 text-sm rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <Flame className="h-4 w-4" />
            History
          </button>
          {!isLoading && !error && (
            <button
              onClick={refreshMetrics}
              disabled={isRefreshing}
              className="px-3 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 text-sm rounded-lg transition-colors duration-200"
              title="Refresh metrics"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
